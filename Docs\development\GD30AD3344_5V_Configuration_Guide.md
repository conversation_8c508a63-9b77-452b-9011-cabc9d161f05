# GD30AD3344 5V电压采集配置指南

## 文档信息
- **项目名称**: GD30AD3344 5V电压测量配置
- **创建日期**: 2025-01-08
- **负责人**: <PERSON> (工程师)
- **版本**: v1.0

## 🎯 **配置目标**
在GD30AD3344上采集5V基准电压信号

## ⚙️ **最佳配置参数**

### **关键配置**
```c
GD30AD3344_InitStruct.MUX = 4;        // AIN0~GND 单端输入
GD30AD3344_InitStruct.PGA = 0;        // ±6.144V 范围 (关键配置!)
GD30AD3344_InitStruct.MODE = 0;       // 连续转换模式
GD30AD3344_InitStruct.DR = 4;         // 100SPS 采样率
```

### **参数详解**

#### 🔧 **PGA设置 (最重要)**
- **必须选择 PGA = 0**
- **输入范围**: ±6.144V
- **原因**: 这是唯一能安全测量5V信号的范围设置
- **其他PGA设置都无法测量5V**:
  - PGA=1: ±4.096V (不够，5V会超出范围)
  - PGA=2: ±2.048V (默认，但远远不够)

#### 📡 **MUX设置**
- **推荐 MUX = 4** (AIN0~GND)
- **输入类型**: 单端输入
- **连接方式**: 将5V信号连接到AIN0引脚，GND连接到地

#### ⏱️ **采样率设置**
| DR值 | 采样率 | 适用场景 |
|------|--------|----------|
| 0 | 6.25SPS | 超高精度，慢速变化 |
| 1 | 12.5SPS | 高精度，慢速变化 |
| 4 | 100SPS | **推荐，平衡精度和速度** |
| 6 | 500SPS | 快速响应 |
| 7 | 1000SPS | 最快响应 |

## 💻 **完整代码实现**

### **初始化函数**
```c
void GD30AD3344_5V_Init(void)
{
    // 配置用于5V电压测量
    GD30AD3344_InitStruct.SS         = 0;        // 无作用
    GD30AD3344_InitStruct.MUX        = 4;        // AIN0~GND 单端输入
    GD30AD3344_InitStruct.PGA        = 0;        // ±6.144V 范围 (关键!)
    GD30AD3344_InitStruct.MODE       = 0;        // 连续转换模式
    GD30AD3344_InitStruct.DR         = 4;        // 100SPS 采样率
    GD30AD3344_InitStruct.RESERVED_1 = 1;        // 保留位
    GD30AD3344_InitStruct.PULL_UP_EN = 0;        // 关闭上拉
    GD30AD3344_InitStruct.NOP        = 1;        // 更新配置
    GD30AD3344_InitStruct.RESERVED   = 1;        // 保留位
    
    // 发送配置到芯片
    spi_gd30ad3344_send_halfword_dma(GD30AD3344_InitStruct_Value);
}
```

### **5V电压读取函数**
```c
float GD30AD3344_Read_5V_Voltage(void)
{
    uint16_t raw_data;
    float voltage;
    int16_t signed_data;
    
    // 读取ADC原始数据
    raw_data = spi_gd30ad3344_send_halfword_dma(GD30AD3344_InitStruct_Value);
    
    // 转换为有符号数据
    signed_data = (int16_t)raw_data;
    
    // 转换为实际电压值
    // PGA=0时，满量程为±6.144V
    voltage = ((float)signed_data / 32767.0f) * 6.144f;
    
    // 确保电压为正值（5V应该是正电压）
    if (voltage < 0) {
        voltage = 0;
    }
    
    return voltage;
}
```

### **优化版本（带滤波）**
```c
float GD30AD3344_Read_5V_Voltage_Filtered(uint8_t samples)
{
    float voltage_sum = 0;
    float voltage_avg;
    
    // 多次采样求平均，提高精度
    for (uint8_t i = 0; i < samples; i++) {
        voltage_sum += GD30AD3344_Read_5V_Voltage();
        delay_1ms(10);  // 采样间隔
    }
    
    voltage_avg = voltage_sum / samples;
    return voltage_avg;
}
```

## 📊 **精度分析**

### **理论精度**
- **分辨率**: 16位
- **电压范围**: ±6.144V (总共12.288V)
- **最小分辨率**: 12.288V / 65536 = **187.5μV**
- **对于5V信号**: 精度约为 **0.00375%**

### **实际精度考虑**
- **噪声影响**: ±1-2 LSB
- **温度漂移**: 需要考虑温度补偿
- **参考电压稳定性**: 内部参考电压的稳定性

## ⚠️ **重要注意事项**

### **安全保护**
1. **输入电压限制**: 绝对不能超过6.144V
2. **过压保护**: 建议在输入端加入保护电路
3. **ESD保护**: 添加ESD保护器件

### **硬件连接**
```
5V信号源 ----[保护电阻]---- AIN0 (GD30AD3344)
GND ----------------------------- GND
```

### **信号调理建议**
如果需要更高精度，可以考虑：
1. **分压电路**: 将5V分压到更小范围（如2.048V）
2. **缓冲放大器**: 提高输入阻抗
3. **滤波电路**: 减少噪声干扰

## 🔧 **调试和验证**

### **验证步骤**
1. **配置验证**: 确认PGA=0设置生效
2. **基准测试**: 使用精密电压源验证读数
3. **稳定性测试**: 长时间监测电压稳定性
4. **温度测试**: 不同温度下的精度验证

### **常见问题排查**
- **读数为0**: 检查PGA设置和连接
- **读数异常**: 确认输入电压未超过6.144V
- **精度不够**: 考虑多次采样平均或硬件滤波

## 📈 **性能优化建议**

1. **采样率优化**: 根据应用需求选择合适的采样率
2. **软件滤波**: 实现移动平均或卡尔曼滤波
3. **校准功能**: 定期使用已知电压源校准
4. **温度补偿**: 根据温度调整读数

## 📝 **总结**

对于5V电压采集，**关键是将PGA设置为0 (±6.144V范围)**，这是唯一能安全测量5V信号的配置。配合适当的采样率和软件滤波，可以实现高精度的5V电压测量。